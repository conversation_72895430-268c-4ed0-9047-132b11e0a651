import {injectable, BindingScope, service} from '@loopback/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  Collection,
  CollectionWithRelations,
  FacetValue,
  FilterGroup,
  FilterValue,
  Product,
  ProductFacetValue,
  ProductVariant,
  ProductVariantFacetValue,
  ProductVariantWithRelations,
} from '../models';
import {Filter, repository, Where} from '@loopback/repository';
import {ProductVariantService} from './product-variant.service';
import {SearchFilterRepository} from '../repositories';
import {FILTER_CACHE_TTL, MINIMUM_DISCOUNT_THRESHOLD} from '../constants';
import {getDiscountLabel, getProductSearchFilter} from '../utils/search.util';

@injectable({scope: BindingScope.TRANSIENT})
export class SearchService {
  constructor(
    @restService(Product)
    private readonly productProxy: ModifiedRestService<Product>,
    @restService(ProductVariant)
    private readonly productVariantProxy: ModifiedRestService<ProductVariant>,
    @restService(ProductVariantFacetValue)
    private readonly productVariantFacetValueProxy: ModifiedRestService<ProductVariantFacetValue>,
    @restService(ProductFacetValue)
    private readonly productFacetValueProxy: ModifiedRestService<ProductFacetValue>,
    @restService(FacetValue)
    private readonly facetValueProxy: ModifiedRestService<FacetValue>,
    @restService(Collection)
    private readonly collectionProxy: ModifiedRestService<Collection>,
    @service(ProductVariantService)
    private readonly productVariantService: ProductVariantService,
    @repository(SearchFilterRepository)
    private readonly searchFilterRepository: SearchFilterRepository,
  ) {}

  private async prepareCondition(
    keyword: string,
  ): Promise<Where<ProductVariant>> {
    const cachedFilter = await this.searchFilterRepository.get(keyword);
    if (cachedFilter?.where) {
      return cachedFilter.where;
    }
    const facetValues = await this.facetValueProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {name: true, id: true},
    });

    const collections = await this.collectionProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {name: true, id: true},
      include: [
        {relation: 'childrens', scope: {fields: {name: true, id: true}}},
      ],
    });

    const productFacetValues = await this.productFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productId: true},
    });

    const productVariantFacets = await this.productVariantFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productVariantId: true},
    });
    const collectionIds = (collections as CollectionWithRelations[]).reduce(
      (acc: string[], collection) => {
        acc.push(collection.id ?? ''); // add parent ID
        if (collection.childrens && Array.isArray(collection.childrens)) {
          acc.push(...collection.childrens.map(child => child.id ?? '')); // add children IDs
        }
        if (collection.parentId) {
          acc.push(collection.parentId);
        }
        return acc;
      },
      [],
    );

    const products = await this.productProxy.find({
      where: {
        or: [
          {collectionId: {inq: collectionIds}},
          {name: {ilike: `%${keyword}%`}},
        ],
      },
    });

    const productIds = [
      ...products.map(p => p.id ?? ''),
      ...productFacetValues.map(pf => pf.productId ?? ''),
    ];
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (productIds.length > 0) {
      where.or.push({productId: {inq: productIds}});
    }
    if (productVariantFacets.length) {
      where.or.push({
        id: {inq: productVariantFacets.map(pvf => pvf.productVariantId ?? '')},
      });
    }
    await this.searchFilterRepository.set(
      keyword,
      {keyword, where},
      {ttl: FILTER_CACHE_TTL},
    );
    return where;
  }

  private async prepareConditionFromFacetOrCollection({
    keyword,
    facetValueIds,
    collectionIds,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
  }): Promise<Where<ProductVariant>> {
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (collectionIds?.length) {
      const products = await this.productProxy.find({
        where: {
          collectionId: {inq: collectionIds},
        },
        fields: {id: true},
      });
      const productIds = products.map(p => p.id ?? '');
      where.or.push({productId: {inq: productIds}});
    }

    if (facetValueIds?.length) {
      const facetVariantMappings =
        await this.productVariantFacetValueProxy.find({
          where: {
            facetValueId: {inq: facetValueIds},
          },
          fields: {productVariantId: true},
        });
      const variantIds = facetVariantMappings.map(
        m => m.productVariantId ?? '',
      );
      if (variantIds.length) {
        where.or.push({id: {inq: variantIds}});
      }
    }

    return where;
  }

  async searchSuggestions(keyword: string): Promise<Partial<ProductVariant[]>> {
    const where = await this.prepareCondition(keyword);
    const products = await this.productVariantProxy.find({
      where,
      fields: {id: true, name: true},
      include: [
        {
          relation: 'product',
          scope: {fields: {sellerId: true}},
        },
      ],
    });

    const filteredProductsFromInactiveSeller =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        products as ProductVariantWithRelations[],
      );
    return filteredProductsFromInactiveSeller;
  }

  async search({
    keyword,
    facetValueIds,
    collectionIds,
    filter,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
    filter?: Filter<ProductVariant>;
  }): Promise<ProductVariant[]> {
    let where: Where<ProductVariant> = filter?.where || {};

    if (keyword) {
      const keywordWhere = await this.prepareCondition(keyword);
      where = {...where, ...keywordWhere};
    }

    if (facetValueIds?.length) {
      const pvFacetMatches = await this.productVariantFacetValueProxy.find({
        where: {facetValueId: {inq: facetValueIds}},
        fields: {productVariantId: true},
      });
      const matchedVariantIds = pvFacetMatches.map(
        v => v.productVariantId ?? '',
      );
      where = {
        ...where,
        id: {inq: matchedVariantIds},
      };
    }

    if (collectionIds?.length) {
      const productMatches = await this.productProxy.find({
        where: {collectionId: {inq: collectionIds}},
        fields: {id: true},
      });
      const productIds = productMatches.map(p => p.id ?? '');
      where = {
        ...where,
        productId: {inq: productIds},
      };
    }

    const mergedIncludes = [
      ...(filter?.include ?? []),
      {
        relation: 'product',
        scope: {fields: {sellerId: true}},
      },
    ];

    const variants = await this.productVariantProxy.find({
      ...filter,
      where,
      include: mergedIncludes,
    });

    const filtered =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        variants as ProductVariantWithRelations[],
      );

    return Promise.all(
      filtered.map(item =>
        this.productVariantService.getProductVariantWithPresignedUrl(item),
      ),
    );
  }

  async getFilters(
    keyword?: string,
    facetValueIdsStr?: string,
    collectionIdsStr?: string,
  ): Promise<FilterGroup[]> {
    const facetValueIds = facetValueIdsStr?.split(',').filter(Boolean);
    const collectionIds = collectionIdsStr?.split(',').filter(Boolean);

    let where: Where<ProductVariant>;

    if (facetValueIds?.length || collectionIds?.length) {
      // build filter conditions manually
      where = await this.prepareConditionFromFacetOrCollection({
        keyword,
        facetValueIds,
        collectionIds,
      });
    } else {
      // fallback to keyword-only search
      where = await this.prepareCondition(keyword ?? '');
    }

    const variants = await this.productVariantProxy.find({
      ...getProductSearchFilter(),
      where,
    });

    return this.buildFilters(variants as ProductVariantWithRelations[]);
  }

  private async buildFilters(
    variants: ProductVariantWithRelations[],
  ): Promise<FilterGroup[]> {
    let collectionMap: Map<string, {variantIds: string[]; name: string}> =
      new Map();
    let discountMap: Map<string, {name: string; variantIds: string[]}> =
      new Map();
    let customMap: Map<string, {name: string; variantIds: string[]}> =
      new Map();
    const offerMap: Map<string, {name: string; variantIds: string[]}> =
      new Map();
    const colorMap: Map<string, {name: string; variantIds: string[]}> =
      new Map();
    let priceMap: Map<string, {name: string; variantIds: string[]}> = new Map();

    for (const variant of variants) {
      const variantId = variant.id!;

      // Collection filter
      collectionMap = this.prepareFilterForCollection(
        variant,
        collectionMap,
        variantId,
      );

      // Discount filter
      discountMap = this.prepareFilterForDiscount(
        variant,
        discountMap,
        variantId,
      );

      // Price filter
      priceMap = this.prepareFilterForPrice(variant, priceMap, variantId);

      // Custom filter
      customMap = this.prepareCustomFilter(variant, customMap, variantId);

      // Offer filter (if it exists on variant)
      //   const offerType = (variant as any).offerType;
      //   if (offerType) {
      //     const object = offerMap.get(offerType) ?? {
      //       name: offerType,
      //       variantIds: [],
      //     };
      //     object.variantIds.push(variantId);
      //     offerMap.set(offerType, object);
      //   }

      //   // Color filter
      //   const color = (variant as any).color;
      //   if (color) {
      //     const colorKey = color.toUpperCase();
      //     const object = colorMap.get(colorKey) ?? {
      //       name: colorKey,
      //       variantIds: [],
      //     };
      //     object.variantIds.push(variantId);
      //     colorMap.set(colorKey, object);
      //   }
    }

    const filters: FilterGroup[] = [];

    filters.push(
      new FilterGroup({
        label: 'Pick a Category',
        values: Array.from(collectionMap.entries()).map(([colId, value]) => {
          return new FilterValue({
            label: value.name,
            value: colId,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Discounts',
        values: Array.from(discountMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Price',
        values: Array.from(priceMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Colour',
        values: Array.from(colorMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Offer',
        values: Array.from(offerMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    filters.push(
      new FilterGroup({
        label: 'Custom',
        values: Array.from(customMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    return filters;
  }

  private prepareFilterForCollection(
    variant: ProductVariantWithRelations,
    collectionMap: Map<string, {variantIds: string[]; name: string}>,
    variantId: string,
  ) {
    if (variant.product?.collection) {
      const object = collectionMap.get(variant.product.collectionId) ?? {
        variantIds: [],
        name: variant.product.collection?.name,
      };
      object.variantIds.push(variantId);
      collectionMap.set(variant.product.collectionId, object);
    }
    return collectionMap;
  }

  private prepareFilterForDiscount(
    variant: ProductVariantWithRelations,
    discountMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantPrice) {
      const {mrp, price} = variant.productVariantPrice;
      const discount = ((mrp - price) / mrp) * 100;
      if (discount >= MINIMUM_DISCOUNT_THRESHOLD) {
        const discountLabel = getDiscountLabel(discount);
        if (discountLabel) {
          const object = discountMap.get(discountLabel) ?? {
            variantIds: [],
            name: discountLabel,
          };
          object.variantIds.push(variantId);
          discountMap.set(discountLabel, object);
        }
      }
    }
    return discountMap;
  }

  private getPriceRangeLabel(price: number): string {
    if (price <= 999) return '₹0 – ₹999';
    if (price <= 1999) return '₹1000 – ₹1999';
    if (price <= 2999) return '₹2000 – ₹2999';
    return '₹3000+';
  }

  private prepareFilterForPrice(
    variant: ProductVariantWithRelations,
    priceMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantPrice) {
      const priceValue = variant.productVariantPrice.price ?? 0;
      const priceLabel = this.getPriceRangeLabel(priceValue);
      const priceObject = priceMap.get(priceLabel) ?? {
        name: priceLabel,
        variantIds: [],
      };
      priceObject.variantIds.push(variantId);
      priceMap.set(priceLabel, priceObject);
    }
    return priceMap;
  }

  private prepareCustomFilter(
    variant: ProductVariantWithRelations,
    customMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.product?.productCustomizationFields) {
      const object = customMap.get('Personalized') ?? {
        variantIds: [],
        name: 'Personalized',
      };
      object.variantIds.push(variantId);
      customMap.set('Personalized', object);
    }
    return customMap;
  }

  private prepareColorFilter(
    variant: ProductVariantWithRelations,
    colorMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantOptions?.length) {
    }
    return colorMap;
  }
}
