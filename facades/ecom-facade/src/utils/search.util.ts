import {Filter} from '@loopback/repository';
import {ProductVariant} from '../models';
import {metaFields} from '../constants';

export const getProductSearchFilter = (): Filter<ProductVariant> => {
  return {
    include: [
      {
        relation: 'product',
        scope: {
          fields: metaFields.reduce(
            (acc, field) => {
              acc[field] = false;
              return acc;
            },
            {} as Record<string, boolean>,
          ),
          include: [
            {
              relation: 'collection',
              scope: {
                fields: metaFields.reduce(
                  (acc, field) => {
                    acc[field] = false;
                    return acc;
                  },
                  {} as Record<string, boolean>,
                ),
              },
            },
          ],
        },
      },

      {
        relation: 'productVariantPrice',
        scope: {
          fields: metaFields.reduce(
            (acc, field) => {
              acc[field] = false;
              return acc;
            },
            {} as Record<string, boolean>,
          ),
        },
      },

      {
        relation: 'productVariantOptions',
        scope: {
          fields: metaFields.reduce(
            (acc, field) => {
              acc[field] = false;
              return acc;
            },
            {} as Record<string, boolean>,
          ),
          include: [
            {
              relation: 'productOption',
              scope: {
                fields: metaFields.reduce(
                  (acc, field) => {
                    acc[field] = false;
                    return acc;
                  },
                  {} as Record<string, boolean>,
                ),
                include: [
                  {
                    relation: 'productOptionGroup',
                    scope: {
                      fields: metaFields.reduce(
                        (acc, field) => {
                          acc[field] = false;
                          return acc;
                        },
                        {} as Record<string, boolean>,
                      ),
                    },
                  },
                ],
              },
            },
          ],
        },
      },
    ],
  };
};

export function getDiscountLabel(discount: number): string | null {
  const ranges = [90, 80, 70, 60, 50, 40, 30, 20, 10];
  const matchedRange = ranges.find(range => discount >= range);

  return matchedRange ? `${matchedRange}% or more` : null;
}
