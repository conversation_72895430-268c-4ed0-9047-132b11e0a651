'use client';

import React, {FC, useState} from 'react';
import {
  Box,
  Typography,
  Chip,
  MenuItem,
  Select,
  FormControl,
  Grid,
  SelectChangeEvent,
} from '@mui/material';
import {FilterValue} from 'types/filter';

interface Props {
  facets: FilterValue[];
  selectedFacet?: string;
  sortOrder: 'featured' | 'lowToHigh' | 'highToLow';
  onSortChange: (value: 'featured' | 'lowToHigh' | 'highToLow') => void;
  onFacetChange?: (value: string) => void;
}

const FilterSortProducts: FC<Props> = ({
  facets,
  selectedFacet,
  sortOrder,
  onSortChange,
  onFacetChange,
}) => {
  console.log('🚀 ~ facets:', facets);
  const [open, setOpen] = useState(false);

  const handleSortChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value as 'featured' | 'lowToHigh' | 'highToLow';
    onSortChange(value);
  };

  return (
    <Grid container spacing={2} alignItems="center" sx={{px: 2, py: 3}}>
      {/* Left: Title */}
      <Grid item xs={12} sm={6}>
        <Typography variant="h6" fontWeight={700} color="#0C004D">
          Laptops{' '}
          <Typography
            component="span"
            variant="body2"
            fontWeight={400}
            sx={{color: '#5F6C7B'}}
          >
            (2800 Products)
          </Typography>
        </Typography>
      </Grid>

      <Grid
        item
        xs={12}
        sm={6}
        sx={{display: 'flex', justifyContent: 'flex-end'}}
      >
        <FormControl>
          <Select
            value={sortOrder}
            open={open}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            onChange={handleSortChange}
            displayEmpty
            inputProps={{'aria-label': 'Sort by'}}
            onMouseEnter={() => setOpen(true)}
            onMouseLeave={() => setOpen(false)}
            sx={{
              width: 240,
              borderRadius: '999px',
              px: 2,
              height: 40,
              fontWeight: 600,
              fontSize: '0.9rem',
              border: '1px solid #9E2FAE',
              color: '#0C004D',
              backgroundColor: '#fff',
              '& .MuiSelect-select': {
                display: 'flex',
                alignItems: 'center',
                height: '40px',
                padding: 0,
              },
              '& fieldset': {border: 'none'},
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  backgroundColor: '#00004A',
                  color: '#fff',
                  borderRadius: '20px',
                  mt: 1,
                  '& .MuiMenuItem-root': {
                    fontSize: '0.9rem',
                    '&.Mui-selected': {
                      backgroundColor: '#9E2FAE',
                    },
                    '&:hover': {
                      backgroundColor: '#9E2FAE',
                    },
                  },
                },
              },
            }}
            renderValue={() => (
              <Box display="flex" alignItems="center" gap={1}>
                <Typography fontWeight={600} fontSize="0.9rem" color="#0C004D">
                  Sort by :
                </Typography>
                <Typography fontWeight={700} fontSize="0.9rem" color="#0C004D">
                  {sortOrder === 'featured'
                    ? 'Relevancy'
                    : sortOrder === 'lowToHigh'
                      ? 'Price low to high'
                      : 'Price high to low'}
                </Typography>
              </Box>
            )}
          >
            <MenuItem value="featured">Relevancy</MenuItem>
            <MenuItem value="lowToHigh" sx={{mt: 1}}>
              Price low to high
            </MenuItem>
            <MenuItem value="highToLow" sx={{mt: 1}}>
              Price high to low
            </MenuItem>
          </Select>
        </FormControl>
      </Grid>

      {/* Filters */}
      <Grid item xs={12}>
        <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
          <Typography variant="body2" fontWeight={600}>
            Quick Filters
          </Typography>
          {facets.map(facet => {
            const isSelected = selectedFacet === facet.value;
            return (
              <Chip
                key={facet.value}
                label={facet.label}
                clickable
                onClick={() => onFacetChange?.(facet.value)}
                sx={{
                  borderRadius: '999px',
                  fontSize: '0.85rem',
                  fontWeight: 500,
                  px: 2,
                  height: 36,
                  backgroundColor: isSelected ? '#9E2FAE' : 'transparent',
                  color: isSelected ? '#fff' : '#000',
                  border: isSelected ? 'none' : '1px solid #ccc',
                }}
              />
            );
          })}
        </Box>
      </Grid>
    </Grid>
  );
};

export default FilterSortProducts;
