// 'use client';
// import React, {useState} from 'react';
// import {
//   Card,
//   CardContent,
//   Typography,
//   Checkbox,
//   FormGroup,
//   FormControlLabel,
//   Button,
//   Drawer,
//   Box,
//   useMediaQuery,
//   useTheme,
//   IconButton,
// } from '@mui/material';
// import {ArrowLeft} from 'iconsax-react';
// import {FilterGroup, FilterValue} from 'types/filter';

// interface ProductFiltersProps {
//   filters: FilterGroup[];
//   onToggleFilter: (filter: FilterValue) => void;
//   appliedFilters: FilterValue[];
// }

// const ProductFilters: React.FC<ProductFiltersProps> = ({
//   filters,
//   onToggleFilter,
//   appliedFilters,
// }) => {
//   const theme = useTheme();
//   const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
//   const [isDrawerOpen, setIsDrawerOpen] = useState(false);

//   const isOptionChecked = (label: string, value: string) => {
//     return appliedFilters.some(f => f.label === label && f.value === value);
//   };

//   const handleDynamicSelection = (
//     sectionLabel: string,
//     optionLabel: string,
//     optionValue: string,
//   ) => {
//     const selectedFilter = filters
//       .find(group => group.label === sectionLabel)
//       ?.values.find(val => val.value === optionValue);

//     if (selectedFilter) {
//       onToggleFilter({
//         label: optionLabel,
//         value: selectedFilter.value,
//         productVariantIds: selectedFilter.productVariantIds,
//       });
//     }
//   };

//   const filterContent = (
//     <Box sx={{width: isMobile ? '100vw' : 230, p: 2}}>
//       {isMobile && (
//         <IconButton onClick={() => setIsDrawerOpen(false)} sx={{mb: 2}}>
//           <ArrowLeft />
//         </IconButton>
//       )}
//       <Typography variant="h5" fontWeight="bold" color="primary">
//         Filters
//       </Typography>
//       {filters
//         .filter(filter => filter.values && filter.values.length > 0)
//         .map(filter => (
//           <React.Fragment key={filter.label}>
//             <Typography variant="subtitle1" fontWeight="bold" mt={2}>
//               {filter.label}
//             </Typography>
//             <FormGroup>
//               {filter.values.map(option => (
//                 <FormControlLabel
//                   key={option.value}
//                   control={
//                     <Checkbox
//                       checked={isOptionChecked(option.label, option.value)}
//                       onChange={() =>
//                         handleDynamicSelection(
//                           filter.label,
//                           option.label,
//                           option.value,
//                         )
//                       }
//                       sx={{color: 'navy'}}
//                     />
//                   }
//                   label={option.label}
//                 />
//               ))}
//             </FormGroup>
//           </React.Fragment>
//         ))}
//     </Box>
//   );

//   return (
//     <>
//       {isMobile ? (
//         <Button variant="contained" onClick={() => setIsDrawerOpen(true)}>
//           Open Filters
//         </Button>
//       ) : (
//         <Card
//           sx={{
//             width: {xs: 250, md: 280},
//             p: 2,
//             boxShadow: 1,
//             borderRadius: 2,
//             backgroundColor: '#edeff2',
//           }}
//         >
//           <CardContent>{filterContent}</CardContent>
//         </Card>
//       )}
//       <Drawer
//         open={isDrawerOpen}
//         onClose={() => setIsDrawerOpen(false)}
//         anchor="right"
//       >
//         {filterContent}
//       </Drawer>
//     </>
//   );
// };

// export default ProductFilters;
'use client';

import React, {useState} from 'react';
import {
  Card,
  CardContent,
  Typography,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Button,
  Drawer,
  Box,
  useMediaQuery,
  useTheme,
  IconButton,
  Slider,
} from '@mui/material';
import {ArrowLeft} from 'iconsax-react';
import {FilterGroup, FilterValue} from 'types/filter';

interface ProductFiltersProps {
  filters: FilterGroup[];
  onToggleFilter: (filter: FilterValue) => void;
  appliedFilters: FilterValue[];
}

const MIN_PRICE = 0;
const MAX_PRICE = 3000;

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onToggleFilter,
  appliedFilters,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [priceRange, setPriceRange] = useState<number[]>([
    MIN_PRICE,
    MAX_PRICE,
  ]);

  const isOptionChecked = (label: string, value: string) => {
    return appliedFilters.some(f => f.label === label && f.value === value);
  };

  const handleDynamicSelection = (
    sectionLabel: string,
    optionLabel: string,
    optionValue: string,
  ) => {
    const selectedFilter = filters
      .find(group => group.label === sectionLabel)
      ?.values.find(val => val.value === optionValue);

    if (selectedFilter) {
      onToggleFilter({
        label: optionLabel,
        value: selectedFilter.value,
        productVariantIds: selectedFilter.productVariantIds,
      });
    }
  };

  const handlePriceChange = (event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as number[]);
    // Optionally trigger a filter update callback here
  };

  const filterContent = (
    <Box sx={{width: isMobile ? '100vw' : 230, p: 2}}>
      {isMobile && (
        <IconButton onClick={() => setIsDrawerOpen(false)} sx={{mb: 2}}>
          <ArrowLeft />
        </IconButton>
      )}
      <Typography variant="h5" fontWeight="bold" color="primary">
        Filters
      </Typography>

      {/* Price Slider */}
      <Box mt={2}>
        <Typography variant="subtitle1" fontWeight="bold">
          Price
        </Typography>

        <Slider
          value={priceRange}
          onChange={handlePriceChange}
          valueLabelDisplay="auto"
          min={MIN_PRICE}
          max={MAX_PRICE}
          sx={{
            color: 'navy',
            mt: 2,
            '& .MuiSlider-thumb': {
              borderRadius: '50%',
            },
          }}
        />

        <Box display="flex" justifyContent="space-between" mt={1}>
          <Box
            sx={{
              px: 2,
              py: 0.5,
              border: '1px solid navy',
              borderRadius: '20px',
              minWidth: 80,
              textAlign: 'center',
              fontWeight: 500,
            }}
          >
            ₹{priceRange[0]}
          </Box>
          <Typography>to</Typography>
          <Box
            sx={{
              px: 2,
              py: 0.5,
              border: '1px solid navy',
              borderRadius: '20px',
              minWidth: 80,
              textAlign: 'center',
              fontWeight: 500,
            }}
          >
            ₹{priceRange[1]}
          </Box>
        </Box>
      </Box>

      {/* Dynamic Filters */}
      {filters
        .filter(filter => filter.values && filter.values.length > 0)
        .map(filter => (
          <React.Fragment key={filter.label}>
            <Typography variant="subtitle1" fontWeight="bold" mt={2}>
              {filter.label}
            </Typography>
            <FormGroup>
              {filter.values
                .sort((a, b) => a.label.localeCompare(b.label))
                .map(option => (
                  <FormControlLabel
                    key={option.value}
                    control={
                      <Checkbox
                        checked={isOptionChecked(option.label, option.value)}
                        onChange={() =>
                          handleDynamicSelection(
                            filter.label,
                            option.label,
                            option.value,
                          )
                        }
                        sx={{color: 'navy'}}
                      />
                    }
                    label={option.label}
                  />
                ))}
            </FormGroup>
          </React.Fragment>
        ))}
    </Box>
  );

  return (
    <>
      {isMobile ? (
        <Button variant="contained" onClick={() => setIsDrawerOpen(true)}>
          Open Filters
        </Button>
      ) : (
        <Card
          sx={{
            width: {xs: 250, md: 280},
            p: 2,
            boxShadow: 1,
            borderRadius: 2,
            backgroundColor: '#edeff2',
          }}
        >
          <CardContent>{filterContent}</CardContent>
        </Card>
      )}
      <Drawer
        open={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        anchor="right"
      >
        {filterContent}
      </Drawer>
    </>
  );
};

export default ProductFilters;
